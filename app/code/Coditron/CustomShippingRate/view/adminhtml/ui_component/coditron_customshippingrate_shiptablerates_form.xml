<?xml version="1.0" ?>
<form xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd">
	<argument name="data" xsi:type="array">
		<item name="js_config" xsi:type="array">
			<item name="provider" xsi:type="string">coditron_customshippingrate_shiptablerates_form.shiptablerates_form_data_source</item>
		</item>
		<item name="label" xsi:type="string" translate="true">General Information</item>
		<item name="template" xsi:type="string">templates/form/collapsible</item>
	</argument>
	<settings>
		<buttons>
			<button name="back" class="Coditron\CustomShippingRate\Block\Adminhtml\ShipTableRates\Edit\BackButton"/>
			<button name="delete" class="Coditron\CustomShippingRate\Block\Adminhtml\ShipTableRates\Edit\DeleteButton"/>
			<button name="save" class="Coditron\CustomShippingRate\Block\Adminhtml\ShipTableRates\Edit\SaveButton"/>
			<button name="save_and_continue" class="Coditron\CustomShippingRate\Block\Adminhtml\ShipTableRates\Edit\SaveAndContinueButton"/>
		</buttons>
		<namespace>coditron_customshippingrate_shiptablerates_form</namespace>
		<dataScope>data</dataScope>
		<deps>
			<dep>coditron_customshippingrate_shiptablerates_form.shiptablerates_form_data_source</dep>
		</deps>
	</settings>
	<dataSource name="shiptablerates_form_data_source">
		<argument name="data" xsi:type="array">
			<item name="js_config" xsi:type="array">
				<item name="component" xsi:type="string">Magento_Ui/js/form/provider</item>
			</item>
		</argument>
		<settings>
			<submitUrl path="*/*/save"/>
		</settings>
		<dataProvider name="shiptablerates_form_data_source" class="Coditron\CustomShippingRate\Model\ShipTableRates\DataProvider">
			<settings>
				<requestFieldName>shiptablerates_id</requestFieldName>
				<primaryFieldName>shiptablerates_id</primaryFieldName>
			</settings>
		</dataProvider>
	</dataSource>
	<fieldset name="general">
		<settings>
			<label>General</label>
		</settings>
		<field name="countries">
            <argument name="data" xsi:type="array">
                <item name="options" xsi:type="object">Magento\Directory\Model\Config\Source\Country</item>
                <item name="config" xsi:type="array">
                    <item name="dataType" xsi:type="string">text</item>
                    <item name="label" xsi:type="string" translate="true">Countries</item>
                    <item name="source" xsi:type="string">ShipTableRates</item>
                    <item name="formElement" xsi:type="string">multiselect</item>
                    <item name="component" xsi:type="string">Magento_Ui/js/form/element/multiselect</item>
                    <item name="filterOptions" xsi:type="boolean">true</item> <!-- Enables search filter -->
                    <item name="chipsEnabled" xsi:type="boolean">true</item> <!-- Optional: Enables chips UI -->
                    <item name="disableLabel" xsi:type="boolean">true</item> <!-- Optional: Hides label inside -->
                    <item name="multiple" xsi:type="boolean">true</item> <!-- Allows multiple selection -->
                    <item name="sortOrder" xsi:type="number">15</item>
                    <item name="validation" xsi:type="array">
                        <item name="required-entry" xsi:type="boolean">true</item>
                    </item>
                </item>
            </argument>
        </field>
		<field name="courier_name" formElement="input" sortOrder="20">
			<argument name="data" xsi:type="array">
				<item name="config" xsi:type="array">
					<item name="source" xsi:type="string">ShipTableRates</item>
				</item>
			</argument>
			<settings>
				<dataType>text</dataType>
				<label translate="true">Courier Name</label>
				<dataScope>courier_name</dataScope>
				<validation>
					<rule name="required-entry" xsi:type="boolean">true</rule>
				</validation>
			</settings>
		</field>
		<field name="weight" formElement="input" sortOrder="30">
			<argument name="data" xsi:type="array">
				<item name="config" xsi:type="array">
					<item name="source" xsi:type="string">ShipTableRates</item>
					<item name="notice" xsi:type="string" translate="true">Enter Weight(Upto) in Kg</item>
				</item>
			</argument>
			<settings>
				<dataType>text</dataType>
				<label translate="true">Weight</label>
				<dataScope>weight</dataScope>
				<validation>
					<rule name="required-entry" xsi:type="boolean">true</rule>
				</validation>
			</settings>
		</field>
		<field name="shipping_price" formElement="input" sortOrder="40">
			<argument name="data" xsi:type="array">
				<item name="config" xsi:type="array">
					<item name="source" xsi:type="string">ShipTableRates</item>
					<item name="notice" xsi:type="string" translate="true">Shipping Price in USD</item>
				</item>
			</argument>
			<settings>
				<dataType>text</dataType>
				<label translate="true">Shipping Price</label>
				<dataScope>shipping_price</dataScope>
				<validation>
					<rule name="required-entry" xsi:type="boolean">true</rule>
				</validation>
			</settings>
		</field>
		<field name="min_amount" formElement="input" sortOrder="45">
			<argument name="data" xsi:type="array">
				<item name="config" xsi:type="array">
					<item name="source" xsi:type="string">ShipTableRates</item>
					<item name="notice" xsi:type="string" translate="true">Minimum order amount for free shipping (leave empty if not applicable)</item>
				</item>
			</argument>
			<settings>
				<dataType>text</dataType>
				<label translate="true">Minimum Amount for Free Shipping</label>
				<dataScope>min_amount</dataScope>
				<validation>
					<rule name="validate-number" xsi:type="boolean">true</rule>
					<rule name="validate-zero-or-greater" xsi:type="boolean">true</rule>
				</validation>
			</settings>
		</field>
		<field name="seller_id" formElement="input" sortOrder="50">
			<argument name="data" xsi:type="array">
				<item name="config" xsi:type="array">
					<item name="source" xsi:type="string">ShipTableRates</item>
				</item>
			</argument>
			<settings>
				<dataType>text</dataType>
				<label translate="true">Seller Id</label>
				<dataScope>seller_id</dataScope>
				<validation>
					<rule name="required-entry" xsi:type="boolean">true</rule>
				</validation>
			</settings>
		</field>
	</fieldset>
</form>
