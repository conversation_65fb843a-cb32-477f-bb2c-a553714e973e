<?php
/** @var \Coditron\CustomShippingRate\Block\TableRates $block */
$helper = $block->getMpHelper();
$isPartner = $helper->isSeller();
?>
<div class="wk-mpsellercategory-container">
    <?php if ($isPartner == 1): ?>
        <div class="shipping-management-tabs">
            <!-- Tab Navigation -->
            <div class="tab-navigation">
                <ul class="tabs-nav">
                    <li class="tab-item active" data-tab="shipping-rates">
                        <a href="#shipping-rates"><?= $escaper->escapeHtml(__('Shipping Rates')) ?></a>
                    </li>
                    <li class="tab-item" data-tab="free-shipping-thresholds">
                        <a href="#free-shipping-thresholds"><?= $escaper->escapeHtml(__('Free Shipping Thresholds')) ?></a>
                    </li>
                </ul>
            </div>

            <!-- Tab Content -->
            <div class="tab-content">
                <!-- Shipping Rates Tab -->
                <div id="shipping-rates" class="tab-pane active">
                    <div class="tab-header">
                        <h3><?= $escaper->escapeHtml(__('Manage Shipping Rates')) ?></h3>
                        <p><?= $escaper->escapeHtml(__('Configure your shipping rates for different countries and weight ranges.')) ?></p>
                    </div>
                    
                    <div class="page-main-actions">
                        <div class="page-actions-placeholder"></div>
                        <div class="page-actions" data-ui-id="page-actions-toolbar-content-header">
                            <div class="page-actions-inner" data-title='<?= $escaper->escapeHtml(__("New Shipping Method")); ?>'>
                                <div class="page-actions-buttons">
                                    <button id="add" title='<?= $escaper->escapeHtml(__("Add New Method")); ?>' type="button"
                                    class="action- scalable primary wk-ui-grid-btn wk-ui-grid-btn-primary"
                                    onclick="location.href
                                    = '<?= $escaper->escapeHtml($block->getUrl('coditron_customshippingrate/shiptablerates/new'))?>';"
                                    data-ui-id="add-button">
                                        <span><?= $escaper->escapeHtml(__("Add New Method")); ?></span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Existing shipping rates grid will be loaded here -->
                    <?= /* @noEscape */ $block->getChildHtml(); ?>
                </div>

                <!-- Free Shipping Thresholds Tab -->
                <div id="free-shipping-thresholds" class="tab-pane">
                    <div class="tab-header">
                        <h3><?= $escaper->escapeHtml(__('Free Shipping Thresholds')) ?></h3>
                        <p><?= $escaper->escapeHtml(__('Set minimum order amounts for free shipping by country.')) ?></p>
                    </div>
                    
                    <div class="page-main-actions">
                        <div class="page-actions-placeholder"></div>
                        <div class="page-actions" data-ui-id="page-actions-toolbar-content-header">
                            <div class="page-actions-inner" data-title='<?= $escaper->escapeHtml(__("New Free Shipping Threshold")); ?>'>
                                <div class="page-actions-buttons">
                                    <button id="add-threshold" title='<?= $escaper->escapeHtml(__("Add New Threshold")); ?>' type="button"
                                    class="action- scalable primary wk-ui-grid-btn wk-ui-grid-btn-primary"
                                    onclick="location.href
                                    = '<?= $escaper->escapeHtml($block->getUrl('coditron_customshippingrate/shiptablerates/edit', ['threshold' => 1]))?>';"
                                    data-ui-id="add-threshold-button">
                                        <span><?= $escaper->escapeHtml(__("Add New Threshold")); ?></span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Free shipping thresholds table -->
                    <div class="free-shipping-thresholds-table">
                        <div class="admin__data-grid-wrap">
                            <table class="data-grid">
                                <thead>
                                    <tr>
                                        <th class="data-grid-th"><?= $escaper->escapeHtml(__('Country')) ?></th>
                                        <th class="data-grid-th"><?= $escaper->escapeHtml(__('Minimum Order Value')) ?></th>
                                        <th class="data-grid-th"><?= $escaper->escapeHtml(__('Actions')) ?></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $thresholds = $block->getFreeShippingThresholds(); ?>
                                    <?php if (!empty($thresholds)): ?>
                                        <?php foreach ($thresholds as $threshold): ?>
                                            <tr>
                                                <td class="data-grid-td">
                                                    <?= $escaper->escapeHtml($block->getCountryName($threshold->getCountries())) ?>
                                                </td>
                                                <td class="data-grid-td">
                                                    <?= $escaper->escapeHtml($threshold->getMinAmount() ? '$' . number_format($threshold->getMinAmount(), 2) : '-') ?>
                                                </td>
                                                <td class="data-grid-td">
                                                    <a href="<?= $escaper->escapeUrl($block->getUrl('coditron_customshippingrate/shiptablerates/edit', ['id' => $threshold->getShiptableratesId()])) ?>" 
                                                       class="action-edit"><?= $escaper->escapeHtml(__('Edit')) ?></a>
                                                    |
                                                    <a href="<?= $escaper->escapeUrl($block->getUrl('coditron_customshippingrate/shiptablerates/delete', ['id' => $threshold->getShiptableratesId()])) ?>" 
                                                       class="action-delete" 
                                                       onclick="return confirm('<?= $escaper->escapeHtml(__('Are you sure you want to delete this threshold?')) ?>')"><?= $escaper->escapeHtml(__('Delete')) ?></a>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    <?php else: ?>
                                        <tr>
                                            <td colspan="3" class="data-grid-td empty-message">
                                                <?= $escaper->escapeHtml(__('No free shipping thresholds configured yet.')) ?>
                                            </td>
                                        </tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    <?php else: ?>
        <h2 class="wk-mp-error-msg">
            <?= $escaper->escapeHtml(__("To Become Seller Please Contact to Admin.")); ?>
        </h2>
    <?php endif; ?>
</div>

<style>
.shipping-management-tabs {
    margin: 20px 0;
}

.tab-navigation {
    border-bottom: 1px solid #ddd;
    margin-bottom: 20px;
}

.tabs-nav {
    list-style: none;
    margin: 0;
    padding: 0;
    display: flex;
}

.tab-item {
    margin-right: 20px;
}

.tab-item a {
    display: block;
    padding: 12px 20px;
    text-decoration: none;
    color: #666;
    border-bottom: 3px solid transparent;
    transition: all 0.3s ease;
}

.tab-item.active a,
.tab-item a:hover {
    color: #333;
    border-bottom-color: #333;
}

.tab-content {
    min-height: 400px;
}

.tab-pane {
    display: none;
}

.tab-pane.active {
    display: block;
}

.tab-header {
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #eee;
}

.tab-header h3 {
    margin: 0 0 10px 0;
    color: #333;
}

.tab-header p {
    margin: 0;
    color: #666;
    font-size: 14px;
}

.free-shipping-thresholds-table {
    margin-top: 20px;
}

.data-grid {
    width: 100%;
    border-collapse: collapse;
    margin-top: 10px;
}

.data-grid-th,
.data-grid-td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #ddd;
}

.data-grid-th {
    background-color: #f5f5f5;
    font-weight: bold;
    color: #333;
}

.empty-message {
    text-align: center;
    color: #666;
    font-style: italic;
}

.action-edit,
.action-delete {
    color: #333;
    text-decoration: none;
    margin: 0 5px;
}

.action-edit:hover,
.action-delete:hover {
    text-decoration: underline;
}
</style>

<script>
require(['jquery'], function($) {
    $(document).ready(function() {
        // Tab switching functionality
        $('.tab-item').on('click', function(e) {
            e.preventDefault();
            
            var targetTab = $(this).data('tab');
            
            // Remove active class from all tabs and panes
            $('.tab-item').removeClass('active');
            $('.tab-pane').removeClass('active');
            
            // Add active class to clicked tab and corresponding pane
            $(this).addClass('active');
            $('#' + targetTab).addClass('active');
        });
    });
});
</script>
