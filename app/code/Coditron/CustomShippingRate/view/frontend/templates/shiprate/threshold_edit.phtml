<?php
/** @var \Coditron\CustomShippingRate\Block\TableRates $block */
$helper = $block->getMpHelper();
$isPartner = $helper->isSeller();
$backUrl = $block->getStoreUrl().'coditron_customshippingrate/shiptablerates/manage/';

$sellerId = $block->getSellerId();
$shipRate = $block->getShipRate();

$countriesListHtml = $block->getCountries(true, $shipRate->getCountries());
?>
<div class="wk-mpsellercategory-container">
    <?php if ($isPartner == 1): ?>
        <div class="page-title-wrapper">
            <h1 class="page-title">
                <span class="base"><?= $escaper->escapeHtml(__('Free Shipping Threshold')) ?></span>
            </h1>
        </div>
        
        <form action="<?= $escaper->escapeUrl($block->getUrl('coditron_customshippingrate/shiptablerates/save')) ?>"
        enctype="multipart/form-data" method="post" id="form-save-threshold"
        data-mage-init='{"validation":{}}'>
            <div class="fieldset wk-ui-component-container">
                <?= $block->getBlockHtml('formkey') ?>
                <?= $block->getBlockHtml('seller.formkey') ?>
                <input type="hidden" name="id" value="<?= $escaper->escapeHtml($shipRate->getShiptableratesId()) ?>">
                <input type="hidden" name="seller_id" value="<?= $escaper->escapeHtml($sellerId) ?>">
                <input type="hidden" name="is_threshold" value="1">
                
                <!-- Set default values for threshold entries -->
                <input type="hidden" name="courier_name" value="Free Shipping Threshold">
                <input type="hidden" name="service_type" value="threshold">
                <input type="hidden" name="weight" value="0">
                <input type="hidden" name="shipping_price" value="0">
                <input type="hidden" name="free_shipping" value="1">
                <input type="hidden" name="packing_time" value="0">
                <input type="hidden" name="delivery_time" value="0">
                <input type="hidden" name="total_lead_time" value="0">
                <input type="hidden" name="return_address_id" value="0">

                <div class="field required">
                    <label for="countries" class="label">
                        <span><?= $escaper->escapeHtml(__("Countries")); ?></span>
                    </label>
                    <div class="tooltip">
                        <span class="tooltipicon"><?= $escaper->escapeHtml(__('?')) ?></span>
                        <span class="tooltiptext"><?= $escaper->escapeHtml(__('Select countries where this free shipping threshold applies')) ?></span>
                    </div>
                    <div class="control">
                        <?= /* @noEscape */ $countriesListHtml ?>
                    </div>
                </div>

                <div class="field required">
                    <label for="min_amount" class="label">
                        <span><?= $escaper->escapeHtml(__("Minimum Order Amount (USD)")); ?></span>
                    </label>
                    <div class="tooltip">
                        <span class="tooltipicon"><?= $escaper->escapeHtml(__('?')) ?></span>
                        <span class="tooltiptext"><?= $escaper->escapeHtml(__('Minimum order amount required for free shipping')) ?></span>
                    </div>
                    <div class="control">
                        <input type="text" class="input-text required-entry validate-number validate-greater-than-zero" 
                               name="min_amount"
                               data-validate="{required:true, 'validate-number':true, 'validate-greater-than-zero':true}" 
                               title="<?= $escaper->escapeHtml(__("minimum amount")); ?>"
                               id="min_amount" 
                               value="<?= $escaper->escapeHtml($shipRate->getMinAmount() ?? '') ?>"
                               placeholder="<?= $escaper->escapeHtml(__('e.g., 100.00')) ?>">
                    </div>
                </div>

                <div class="actions-toolbar">
                    <div class="primary">
                        <button type="submit" class="action submit primary" title="<?= $escaper->escapeHtml(__('Save Threshold')) ?>">
                            <span><?= $escaper->escapeHtml(__('Save Threshold')) ?></span>
                        </button>
                    </div>
                    <div class="secondary">
                        <button type="button" class="action back" id="back" title="<?= $escaper->escapeHtml(__('Back')) ?>">
                            <span><?= $escaper->escapeHtml(__('Back')) ?></span>
                        </button>
                    </div>
                </div>
            </div>
        </form>
    <?php else: ?>
        <h2 class="wk-mp-error-msg">
            <?= $escaper->escapeHtml(__("To Become Seller Please Contact to Admin.")); ?>
        </h2>
    <?php endif; ?>
</div>

<script>
    require(['jquery'], function($) {
        $(document).ready(function() {
            var back = "<?php echo $backUrl; ?>";
            $("#back").click(function(){
                window.location.replace(back);
            });
        });
    });
</script>

<script type="text/javascript">
    require(['jquery', 'select2'], function ($) {
        $(document).ready(function () {
            $('.custom-multiselect').select2({
                placeholder: "Select countries",
                allowClear: true
            });
        });
    });
</script>

<style>
    .page-title-wrapper {
        margin-bottom: 30px;
        padding-bottom: 15px;
        border-bottom: 1px solid #ddd;
    }
    
    .page-title {
        margin: 0;
        color: #333;
        font-size: 28px;
        font-weight: 300;
    }
    
    .actions-toolbar {
        margin-top: 30px;
        padding-top: 20px;
        border-top: 1px solid #ddd;
    }
    
    .actions-toolbar .primary,
    .actions-toolbar .secondary {
        display: inline-block;
        margin-right: 15px;
    }
    
    .action.submit.primary {
        background-color: #007bdb;
        color: white;
        border: none;
        padding: 12px 24px;
        border-radius: 3px;
        cursor: pointer;
        font-size: 14px;
    }
    
    .action.submit.primary:hover {
        background-color: #0056b3;
    }
    
    .action.back {
        background-color: #f5f5f5;
        color: #333;
        border: 1px solid #ddd;
        padding: 12px 24px;
        border-radius: 3px;
        cursor: pointer;
        font-size: 14px;
    }
    
    .action.back:hover {
        background-color: #e9e9e9;
    }
    
    .select2-search__field {
        height: auto !important;
    }
</style>
