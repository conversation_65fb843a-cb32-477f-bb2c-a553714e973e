<?xml version="1.0"?>

<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" layout="seller-2columns-left" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <head>
        <title>New Free Shipping Threshold</title>

        <css src="Webkul_Marketplace::css/wk_block.css"/>
        <css src="Webkul_Marketplace::css/style.css"/>
        <css src="Webkul_Marketplace::css/product.css"/>
        <css src="Webkul_Marketplace::css/layout.css"/>
        <css src="Coditron_CustomShippingRate::css/select2/select2.css"/>
    </head>
    <body>
        <referenceContainer name="seller.content">
            <block class="Coditron\CustomShippingRate\Block\TableRates" name="mpsellership_threshold_edit" template="Coditron_CustomShippingRate::shiprate/threshold_edit.phtml" cacheable="false"></block>
        </referenceContainer>
    </body>
</page>
