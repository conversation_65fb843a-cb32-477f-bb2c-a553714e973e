<?php
/**
 * Webkul Software.
 *
 * @category  Webkul
 * @package   Webkul_MpSellerCategory
 * <AUTHOR> Software Private Limited
 * @copyright Webkul Software Private Limited (https://webkul.com)
 * @license   https://store.webkul.com/license.html
 */
namespace Coditron\CustomShippingRate\Ui\DataProvider;

use Magento\Framework\Session\SessionManagerInterface;
use Coditron\CustomShippingRate\Model\ResourceModel\ShipTableRates\CollectionFactory;
use Webkul\Marketplace\Helper\Data as HelperData;

class ShippingRateListDataProvider extends \Magento\Ui\DataProvider\AbstractDataProvider
{
    /**
     * @var \Coditron\CustomShippingRate\Model\ResourceModel\ShipTableRates\Collection
     */
    protected $collection;

    /**
     * @var SessionManagerInterface
     */
    protected $session;

    /**
     * @var \Coditron\CustomShippingRate\Helper\Data
     */
    protected $helper;

    /**
     * @param string $name
     * @param string $primaryFieldName
     * @param string $requestFieldName
     * @param CollectionFactory $collectionFactory
     * @param \Coditron\CustomShippingRate\Helper\Data $helper
     * @param array $meta
     * @param array $data
     */
    public function __construct(
        $name,
        $primaryFieldName,
        $requestFieldName,
        CollectionFactory $collectionFactory,
        \Coditron\CustomShippingRate\Helper\Data $helper,
        array $meta = [],
        array $data = []
    ) {
        parent::__construct($name, $primaryFieldName, $requestFieldName, $meta, $data);
        $this->collection = $collectionFactory->create();
        $this->helper = $helper;
        $this->collection->addFieldToFilter(
            'seller_id',
            ['eq' => $this->helper->getSellerId()]
        );

        // Exclude threshold entries from regular shipping rates grid
        $this->collection->addFieldToFilter(
            ['courier_name', 'courier_name'],
            [
                ['neq' => 'Free Shipping Threshold'],
                ['null' => true]
            ]
        );
    }
}
