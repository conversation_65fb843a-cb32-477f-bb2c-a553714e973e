<?php

namespace Co<PERSON>ron\CustomShippingRate\Block;

use Coditron\CustomShippingRate\Api\Data\ShipTableRatesInterface;
use Coditron\CustomShippingRate\Api\ShipTableRatesRepositoryInterface;
use Coditron\CustomShippingRate\Model\ResourceModel\ShipTableRates\CollectionFactory as TableRatesFactory;
use Coditron\CustomShippingRate\Model\ShipTableRatesFactory;
use Coditron\CustomShippingRate\Ui\Component\Listing\Column\Parcel;
use Comave\CustomerAddressType\Model\Config\AddressTypeOptions;
use Magento\Config\ViewModel\CountryFilter;
use Magento\Customer\Api\CustomerRepositoryInterface;
use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\Json\Helper\Data;
use Magento\Framework\View\Element\Template\Context;
use Magento\Store\Model\StoreManagerInterface;

/**
 * class TableRates is used to provide data of TableRates
 */
class TableRates extends \Magento\Framework\View\Element\Template
{
    /**
     * @var \Magento\Framework\Json\Helper\Data
     */
    protected $_jsonHelper;

    /**
     * @var \Coditron\CustomShippingRate\Helper\Data
     */
    protected $_helper;

    /**
     * @var TableRatesFactory
     */
    protected $TableRatesFactory;

    /**
     * @var \Coditron\CustomShippingRate\Api\ShipTableRatesRepositoryInterface
     */
    protected $shiprateRepository;

    /**
     * @var ShipTableRatesFactory
     */
    protected ShipTableRatesFactory $shiprateFactory;

    /**
     * @var \Webkul\Marketplace\Helper\Data
     */
    protected $mpHelper;

    /**
     * @var \Magento\Store\Model\StoreManagerInterface
     */
    protected $_storeManager;

    /**
     * @var \Magento\Framework\App\Config\ScopeConfigInterface
     */
    private $scopeConfig;

    private CountryFilter $countryFilter;

    private CustomerRepositoryInterface $customerRepository;

    /**
     * @param Context $context
     * @param Data $jsonHelper
     * @param \Coditron\CustomShippingRate\Helper\Data $helper
     * @param ShipTableRatesRepositoryInterface $shiprateRepository
     * @param ShipTableRatesFactory $shiprateFactory
     * @param StoreManagerInterface $storeManager
     * @param \Webkul\Marketplace\Helper\Data $mpHelper
     * @param TableRatesFactory $TableRatesFactory
     * @param \Magento\Directory\Block\Data $directoryBlock
     * @param Parcel $parcelOptions
     * @param ScopeConfigInterface $scopeConfig
     * @param CountryFilter $countryFilter
     * @param CustomerRepositoryInterface $customerRepository
     * @param array $data
     */
    public function __construct(
        \Magento\Framework\View\Element\Template\Context $context,
        \Magento\Framework\Json\Helper\Data $jsonHelper,
        \Coditron\CustomShippingRate\Helper\Data $helper,
        \Coditron\CustomShippingRate\Api\ShipTableRatesRepositoryInterface $shiprateRepository,
        ShipTableRatesFactory $shiprateFactory,
        \Magento\Store\Model\StoreManagerInterface $storeManager,
        \Webkul\Marketplace\Helper\Data $mpHelper,
        TableRatesFactory $TableRatesFactory,
        \Magento\Directory\Block\Data $directoryBlock,
        \Coditron\CustomShippingRate\Ui\Component\Listing\Column\Parcel $parcelOptions,
        \Magento\Framework\App\Config\ScopeConfigInterface $scopeConfig,
        \Magento\Config\ViewModel\CountryFilter $countryFilter,
        CustomerRepositoryInterface $customerRepository,
        array $data = []
    ) {
        $this->mpHelper = $mpHelper;
        $this->_jsonHelper = $jsonHelper;
        $this->_helper = $helper;
        $this->_storeManager = $storeManager;
        $this->shiprateRepository = $shiprateRepository;
        $this->shiprateFactory = $shiprateFactory;
        $this->tableRatesFactory = $TableRatesFactory;
        $this->directoryBlock = $directoryBlock;
        $this->parcelOptions = $parcelOptions;
        $this->scopeConfig = $scopeConfig;
        $this->countryFilter = $countryFilter;
        $this->customerRepository = $customerRepository;
        parent::__construct($context, $data);
    }

    public function getCountries(bool $euOnly = true, $defValue = null): string
    {
        if ($euOnly) {
            $countryHtml = $this->getEUCountriesHtmlElement($defValue);
        } else {
            $countryHtml = $this->directoryBlock->getCountryHtmlSelect(
                $defValue,
                'countries[]', // Important: Make it an array for multiple selection
                'countries',
                'Countries'
            );
            // remove empty element, added by toOptionArray inside getCountryHtmlSelect()
            $countryHtml = str_replace('<option value="" selected="selected" > </option>', '', $countryHtml);
        }

        // Inject 'multiple' attribute
        return str_replace('<select', '<select multiple="multiple" class="custom-multiselect"', $countryHtml);
    }

    /**
     * @throws LocalizedException
     */
    public function getEUCountriesHtmlElement($defValue = null, $name = 'countries[]', $id = 'countries', $title = 'Countries'): string
    {
        // Get the list of EU country codes from the config
        $euCountries = $this->countryFilter->getEuCountryList();
        $options = $this->directoryBlock->getCountryCollection()->addCountryCodeFilter($euCountries)->toOptionArray(false);

        $html = $this->directoryBlock->getLayout()->createBlock(
            \Magento\Framework\View\Element\Html\Select::class
        )->setName(
            $name
        )->setId(
            $id
        )->setTitle(
            $this->escapeHtmlAttr(__($title))
        )->setValue(
            $defValue
        )->setOptions(
            $options
        )->setExtraParams(
            'data-validate="{\'validate-select\':true}"'
        )->getHtml();

        return $html;
    }

    /**
     * @return array
     */
    public function getServiceTypeOptions(): array
    {
        $options = [];
        foreach ($this->_helper->getShippingType() as $serviceType) {
            $options[] = [
                'label' => $serviceType['title'],
                'value' => $serviceType['code'],
            ];
        }

        return $options;
    }

    /**
     * @return array
     */
    public function getReturnAddressOptions(): array
    {
        $sellerId = $this->getSellerId();

        $options = [];

        if (!$sellerId) {
            return $options;
        }

        // Load the customer (seller) data
        try {
            $customer = $this->customerRepository->getById($sellerId);
        } catch (NoSuchEntityException|LocalizedException $e) {
            return $options;
        }

        // Get customer addresses
        $sellerAddresses = $customer->getAddresses();

        foreach ($sellerAddresses as $address) {
            // skip non-return type addresses
            if ($address->getCustomAttribute('save_as_address_type')->getValue() !== AddressTypeOptions::OPTION_RETURN) {
                continue;
            }

            // Manually format address
            $formattedAddress = implode(', ', array_filter([
                $address->getStreet() ? implode(' ', $address->getStreet()) : null,
                $address->getCity(),
                $address->getRegion() ? $address->getRegion()->getRegion() : null,
                $address->getPostcode(),
                $address->getCountryId()
            ]));

            $options[] = [
                'label' => $formattedAddress,
                'value' => $address->getId(), // Use address ID as value
            ];
        }

        if (empty($options)) {
            $options[] = [
                'label' => 'Please define return addresses in settings',
                'value' => null,
            ];
        }

        return $options;
    }
    public function getShipRate(?int $rateId = null): ShipTableRatesInterface
    {
        $rateId ??= (int)$this->getRequest()->getParam('shiptablerates_id');

        if (!$rateId) {
            return $this->shiprateFactory->create();
        }

        try {
            return $this->shiprateRepository->get($rateId);
        } catch (\Exception $e) {
            $this->logger->error(__('Error fetching shipping rate: %1', $e->getMessage()));
        }

        return $this->shiprateFactory->create();
    }

    /**
     * Get All Status
     *
     * @return int
     */
    public function getSellerId(): int
    {
        return $this->_helper->getSellerId();
    }

    /**
     * Get All store URL
     * @throws NoSuchEntityException
     */
    public function getStoreUrl(): string
    {
        return $this->_storeManager->getStore()->getBaseUrl();
    }

    /**
     * @throws NoSuchEntityException
     */
    public function getStoreWeightUnits(){
        $storeCode = $this->_storeManager->getStore()->getCode();

        $weightLabel = $this->scopeConfig
            ->getValue('general/locale/weight_unit', \Magento\Store\Model\ScopeInterface::SCOPE_STORE, $storeCode);

        return $weightLabel;
    }

    /**
     * Get Marketplace helper data
     *
     * @return object
     */
    public function getMpHelper()
    {
        return $this->mpHelper;
    }

    /**
     * Get free shipping thresholds for current seller
     *
     * @return array
     */
    public function getFreeShippingThresholds(): array
    {
        $sellerId = $this->getSellerId();
        if (!$sellerId) {
            return [];
        }

        $collection = $this->tableRatesFactory->create();
        $collection->addFieldToFilter('seller_id', $sellerId)
                   ->addFieldToFilter('min_amount', ['notnull' => true])
                   ->addFieldToFilter('min_amount', ['gt' => 0])
                   ->addFieldToFilter('courier_name', 'Free Shipping Threshold');

        return $collection->getItems();
    }

    /**
     * Get regular shipping rates for current seller (excluding thresholds)
     *
     * @return array
     */
    public function getRegularShippingRates(): array
    {
        $sellerId = $this->getSellerId();
        if (!$sellerId) {
            return [];
        }

        $collection = $this->tableRatesFactory->create();
        $collection->addFieldToFilter('seller_id', $sellerId)
                   ->addFieldToFilter(
                       ['courier_name', 'courier_name'],
                       [
                           ['neq' => 'Free Shipping Threshold'],
                           ['null' => true]
                       ]
                   );

        return $collection->getItems();
    }

    /**
     * Get country name by country code
     *
     * @param string|array $countryCodes
     * @return string
     */
    public function getCountryName($countryCodes): string
    {
        if (is_string($countryCodes)) {
            $countryCodes = explode(',', $countryCodes);
        }

        if (!is_array($countryCodes)) {
            return '';
        }

        $countryNames = [];
        $countryCollection = $this->directoryBlock->getCountryCollection();

        foreach ($countryCodes as $countryCode) {
            $country = $countryCollection->getItemById(trim($countryCode));
            if ($country) {
                $countryNames[] = $country->getName();
            }
        }

        return implode(', ', $countryNames);
    }
}
