<?php
/**
 * Webkul Software.
 *
 * @category  Webkul
 * @package   Webkul_MpSellerCategory
 * <AUTHOR> Software Private Limited
 * @copyright Webkul Software Private Limited (https://webkul.com)
 * @license   https://store.webkul.com/license.html
 */
namespace Coditron\CustomShippingRate\Controller\ShipTableRates;

class Save extends \Coditron\CustomShippingRate\Controller\AbstractShiprate
{
    /**
     * Seller Category Save action
     *
     * @return \Magento\Framework\Controller\Result\RedirectFactory
     */
    public function execute()
    {
        /** @var \Magento\Framework\Controller\Result\Redirect $resultRedirect */
        if ($this->getRequest()->isPost()) {
            try {
                if (!$this->_formKeyValidator->validate($this->getRequest())) {
                    return $this->resultRedirectFactory->create()->setPath(
                        'coditron_customshippingrate/shiptablerates/manage',
                        ['_secure' => $this->getRequest()->isSecure()]
                    );
                }

                $postData = $this->getRequest()->getPostValue();
                $postData['seller_id'] = $this->getSellerId();

                // Add logging to debug
                $writer = new \Zend_Log_Writer_Stream(BP . '/var/log/shipping_save_debug.log');
                $logger = new \Zend_Log();
                $logger->addWriter($writer);

                $logger->info('Save Controller - Raw POST data: ' . var_export($postData, true));
                $logger->info('Save Controller - Countries data: ' . var_export($postData['countries'] ?? 'NOT SET', true));
                $logger->info('Save Controller - Countries type: ' . gettype($postData['countries'] ?? null));

                $result = $this->_helper->validateData($postData);
                if ($result['error']) {
                    $this->messageManager->addError(__($result['msg']));
                    return $this->resultRedirectFactory->create()->setPath(
                        'coditron_customshippingrate/shiptablerates/manage',
                        ['_secure' => $this->getRequest()->isSecure()]
                    );
                }

                $sellerShiprate = $this->getSellerShiprate();

                // Process countries array to comma-separated string
                if (isset($postData['countries']) && is_array($postData['countries'])) {
                    $logger->info('Save Controller - Converting countries array to string: ' . var_export($postData['countries'], true));
                    $postData['countries'] = implode(',', $postData['countries']);
                    $logger->info('Save Controller - Countries after conversion: ' . $postData['countries']);
                } else {
                    $logger->info('Save Controller - Countries not array or not set');
                }

                if ($postData['id']) {
                    $sellerShiprate->addData($postData)->setShiptableratesId($postData['id']);
                } else {
                    $sellerShiprate->setData($postData);
                }

                $sellerShiprate->save();
                $id = $sellerShiprate->getShiptableratesId();

                // Check if this is a threshold entry
                $isThreshold = isset($postData['is_threshold']) && $postData['is_threshold'];

                if ($isThreshold) {
                    $this->messageManager->addSuccess(__("Free Shipping Threshold saved successfully."));
                } else {
                    $this->messageManager->addSuccess(__("Shipping Rate saved successfully."));
                }

                $this->_helper->clearCache();

                // Redirect back to manage page for threshold entries, or to edit page for regular rates
                if ($isThreshold) {
                    return $this->resultRedirectFactory->create()->setPath(
                        'coditron_customshippingrate/shiptablerates/manage',
                        ['_secure' => $this->getRequest()->isSecure()]
                    );
                } else {
                    return $this->resultRedirectFactory->create()->setPath(
                        'coditron_customshippingrate/shiptablerates/edit',
                        ['shiptablerates_id' => $id, '_secure' => $this->getRequest()->isSecure()]
                    );
                }
            } catch (\Exception $e) {
                $this->messageManager->addError($e->getMessage());
                return $this->resultRedirectFactory->create()->setPath(
                    'coditron_customshippingrate/shiptablerates/manage',
                    ['_secure' => $this->getRequest()->isSecure()]
                );
            }
        } else {
            return $this->resultRedirectFactory->create()->setPath(
                'coditron_customshippingrate/shiptablerates/manage',
                ['_secure' => $this->getRequest()->isSecure()]
            );
        }
    }
}
