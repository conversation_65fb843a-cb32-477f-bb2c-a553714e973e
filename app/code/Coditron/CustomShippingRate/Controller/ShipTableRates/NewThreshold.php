<?php

namespace Coditron\CustomShippingRate\Controller\ShipTableRates;

/**
 * Class NewThreshold
 * Controller for creating new free shipping thresholds
 */
class NewThreshold extends \Webkul\MpSellerCategory\Controller\AbstractCategory
{
    /**
     * Execute Method
     *
     * @return \Magento\Framework\View\Result\Page
     */
    public function execute()
    {
        if (!$this->_marketplaceHelper->isSeller()) {
            return $this->resultRedirectFactory->create()->setPath(
                'marketplace/account/becomeseller',
                ['_secure' => $this->getRequest()->isSecure()]
            );
        }

        $resultPage = $this->_resultPageFactory->create();
        if ($this->_marketplaceHelper->getIsSeparatePanel()) {
            $resultPage->addHandle('sellership_layout2_rate_edit');
        }

        $resultPage->getConfig()->getTitle()->set(__('New Free Shipping Threshold'));
        return $resultPage;
    }
}
