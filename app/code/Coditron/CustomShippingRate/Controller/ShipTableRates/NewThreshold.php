<?php

namespace Coditron\CustomShippingRate\Controller\ShipTableRates;

/**
 * Class NewThreshold
 * Controller for creating new free shipping thresholds
 */
class NewThreshold extends \Coditron\CustomShippingRate\Controller\AbstractShiprate
{
    /**
     * Execute Method
     *
     * @return \Magento\Framework\View\Result\Page
     */
    public function execute()
    {
        if (!$this->_marketplaceHelper->isSeller()) {
            return $this->resultRedirectFactory->create()->setPath(
                'marketplace/account/becomeseller',
                ['_secure' => $this->getRequest()->isSecure()]
            );
        }

        $resultPage = $this->_resultPageFactory->create();
        if ($this->_marketplaceHelper->getIsSeparatePanel()) {
            $resultPage->addHandle('mpsellership_layout2_rate_newthreshold');
        }

        $resultPage->getConfig()->getTitle()->set(__('New Free Shipping Threshold'));
        return $resultPage;
    }
}
