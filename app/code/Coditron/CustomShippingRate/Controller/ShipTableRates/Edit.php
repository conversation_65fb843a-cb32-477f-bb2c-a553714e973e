<?php

namespace Coditron\CustomShippingRate\Controller\ShipTableRates;

class Edit extends \Coditron\CustomShippingRate\Controller\AbstractShiprate
{
    /**
     * Seller Category Edit action.
     *
     * @return \Magento\Framework\Controller\Result\RedirectFactory
     */
    public function execute()
    {
        if (!$this->_marketplaceHelper->isSeller()) {
            return $this->resultRedirectFactory->create()->setPath(
                'marketplace/account/becomeseller',
                ['_secure' => $this->getRequest()->isSecure()]
            );
        }

        $resultPage = $this->_resultPageFactory->create();

        // Check if this is a threshold creation request
        $isThreshold = $this->getRequest()->getParam('threshold');

        if ($this->_marketplaceHelper->getIsSeparatePanel()) {
            if ($isThreshold) {
                $resultPage->addHandle('mpsellership_layout2_rate_newthreshold');
            } else {
                $resultPage->addHandle('mpsellership_layout2_rate_edit');
            }
        }

        if (!empty($this->getRequest()->getParam("shiptablerates_id"))) {
            $sellerShiprate = $this->getSellerShiprate();
            if (empty($sellerShiprate->getShiptableratesId())) {
                $this->messageManager->addError("Shipping method does not exist");
                return $this->resultRedirectFactory->create()->setPath(
                    'coditron_customshippingrate/shiptablerates/manage',
                    ['_secure' => $this->getRequest()->isSecure()]
                );
            }

            $title = $sellerShiprate->getCourierName();
        } else {
            if ($isThreshold) {
                $title = "New Free Shipping Threshold";
            } else {
                $title = "New Shipping Method";
            }
        }

        $resultPage->getConfig()->getTitle()->set(__($title));
        return $resultPage;
    }
}
