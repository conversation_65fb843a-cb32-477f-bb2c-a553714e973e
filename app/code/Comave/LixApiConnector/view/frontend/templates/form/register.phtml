<?php

$_helper = $this->helper(\Comave\LixApiConnector\Helper\Data::class);

/** @var Magento\Customer\Helper\Address $addressHelper */
$addressHelper = $block->getData('addressHelper');
/** @var \Magento\Directory\Helper\Data $directoryHelper */
$directoryHelper = $block->getData('directoryHelper');
/** @var \Magento\Customer\ViewModel\Address\RegionProvider $regionProvider */
$regionProvider = $block->getRegionProvider();

$formData = $block->getFormData();

$helper = $this->helper('Comave\ApiConnector\Helper\Data');
    $values = $helper->getBaseUrl();


$objectManager = \Magento\Framework\App\ObjectManager::getInstance();
$customModel = $objectManager->get('Comave\Club\Model\Club');

$customerData = $customModel->getCollection()->getData();


$blockClub = $block->getLayout()->createBlock('Comave\TravellerInfo\Block\GetCustomLink');
$customerArray = $blockClub->getClubSellerInfo();

$isTraveller = 0;
$isReferal = 0;
$clubid = $this->getRequest()->getParams();

if(array_key_exists("invitation",$clubid)){
    $isReferal = 1;
}

if(empty($clubid)){
    $clubid['clubid']='';
    $clubid['clubcamp']=' ';
    $clubid['uid']='';
}elseif(!empty($clubid)){
    if(array_key_exists("clubid",$clubid)){
        $_helper->setCookieValue($clubid['clubid'],36000);
    }
    if(!array_key_exists("clubcamp",$clubid)){
        $clubid['clubcamp']=' ';
    }
    if(array_key_exists("uid",$clubid)){
        if($clubid['uid']=="ushop"){
            $isTraveller = 1;
        }
    }
}

$orgId = $_helper->getOrganisationId();
$clubresponce = $_helper->getClubList($orgId);

?>
<?php $displayAll = $block->getConfig('general/region/display_all'); ?>
<?= $block->getChildHtml('form_fields_before') ?>
<?php /* Extensions placeholder */ ?>
<?= $block->getChildHtml('customer.form.register.extra') ?>
<div class="main-register">
<form class="form create account form-create-account"
      action="<?= $escaper->escapeUrl($block->getPostActionUrl()) ?>"
      method="post"
      id="form-validate"
      enctype="multipart/form-data"
      autocomplete="off">
    <?= /* @noEscape */ $block->getBlockHtml('formkey') ?>

    <h1 class="regi-head">Sign up to Get More</h1>
    <!-- Circles which indicates the steps of the form: -->
    <div class="reg-progress">
        <span class="step personal-data">
            <h1>Contact</h1>
        </span>
      <span class="step club-selection">
            <h1>Club</h1>
        </span>
        <span class="step country-selection">
            <h1>Nation</h1>
        </span>
        <span class="step credentials">
            <h1>Password</h1>
        </span>
    </div>

    <fieldset class="fieldset create info">
        <div class="regi-tab">
            <h1>Let us get to know you.</h1>
            <h2>Create a new account</h2>
            <legend class="legend"><span><?= $escaper->escapeHtml(__('Personal Information')) ?></span></legend><br>
            <input type="hidden" name="success_url" value="<?= $escaper->escapeUrl($block->getSuccessUrl()) ?>">
            <input type="hidden" name="error_url" value="<?= $escaper->escapeUrl($block->getErrorUrl()) ?>">
            <?= $block->getLayout()
                    ->createBlock(\Magento\Customer\Block\Widget\Name::class)
                    ->setObject($formData)
                    ->setForceUseCustomerAttributes(true)
                    ->toHtml() ?>
            <?php if ($block->isNewsletterEnabled()): ?>
                <div class="field choice newsletter">
                    <input type="checkbox"
                        name="is_subscribed"
                        title="<?= $escaper->escapeHtmlAttr(__('Sign Up for Newsletter')) ?>"
                        value="1"
                        id="is_subscribed"
                        <?php if ($formData->getIsSubscribed()): ?>checked="checked"<?php endif; ?>
                        class="checkbox">
                    <label for="is_subscribed" class="label">
                        <span><?= $escaper->escapeHtml(__('Sign Up for Newsletter')) ?></span>
                    </label>
                </div>
                <?php /* Extensions placeholder */ ?>
                <?= $block->getChildHtml('customer.form.register.newsletter') ?>
            <?php endif ?>
            <div class="field required">
                <label for="telephone" class="label"><span><?= __('Phone No.') ?></span></label>
                <div class="control">
                    <input type="text" name="phone_no" id="phone_no"  title="<?php echo __('Phone no.') ?>" class="input-text" placeholder="Phone no." data-validate='{"required":true}' maxlength="14">
                    <i class="icon-line2-call-end"></i>
                </div>
            </div>
            <div class="field required">
                <div class="control">
                    <input type="hidden" name="customer_country" id="customer_country" >
                    <input type="hidden" name="dial_code" id="dial_code">

                    <input type="hidden" id="country_codes" name="region">
                    <input type="hidden" name="mobile" id="mobile">
                </div>
            </div>

        </div>
         <div class="regi-tab">

            <h1>Support your team</h1>
            <h2>Choose your team</h2>
            <span><b>Are you a fan of a Sports Club?</b>You will have access to the club exclusive offers and services</span>
            <div class="field club-drop">

                <div class="control">
                    <?php
                    foreach ($customerArray as $key => $customerData) {
                        if (array_key_exists('wkv_club_unique_identfier', $customerData)) {
                            if($clubid['clubcamp']==$customerData['wkv_club_name']){
                    ?>
                                <label>
                                    <input type="radio" id="<?php echo 'customerclub-' . $customerData['wkv_club_unique_identfier']; ?>" name="customerclub" value="<?php echo $customerData['wkv_club_unique_identfier']; ?>" <?php echo "checked"; ?> >
                                    <?php if (array_key_exists('wkv_club_logo', $customerData)){
                                        $logopath = $customerData['wkv_club_logo'];
                                    ?>
                                        <img src="<?php echo $blockClub->getMediaUrl() . 'media/vendorfiles/image/' . $logopath; ?>" >
                                    <?php } ?>
                                </label>
                        <?php
                            }else{  ?>

                                <label>
                                    <input type="radio" id="<?php echo 'customerclub-' . $customerData['wkv_club_unique_identfier']; ?>" name="customerclub" value="<?php echo $customerData['wkv_club_unique_identfier']; ?>">
                                    <?php if (array_key_exists('wkv_club_logo', $customerData)){
                                        $logopath = $customerData['wkv_club_logo'];
                                    ?>
                                        <img src="<?php echo $blockClub->getMediaUrl() . '/vendorfiles/image/' . $logopath; ?>" >
                                    <?php }?>
                                </label>
                        <?php
                            }
                        }
                    }
                    ?>

                </div>
            </div>
        </div>

        <div class="regi-tab">

            <?php  if($isReferal){  ?>
                <div class="create-referral">
                    <label for="referral_code" class="label"><span><?= __('Referral Code') ?></span></label>
                    <div class="control">
                        <input type="text" name="referral_code" id="referral_code"  title="<?php echo __('Referral Code') ?>" class="input-text" placeholder="Referral Code" >
                    </div>
                </div>
            <?php } ?>

            <?php $_dob = $block->getLayout()->createBlock(\Magento\Customer\Block\Widget\Dob::class) ?>
            <?php if ($_dob->isEnabled()): ?>
                <?= $_dob->setDate($formData->getDob())->toHtml() ?>
            <?php endif ?>

            <?php $_taxvat = $block->getLayout()->createBlock(\Magento\Customer\Block\Widget\Taxvat::class) ?>
            <?php if ($_taxvat->isEnabled()): ?>
                <?= $_taxvat->setTaxvat($formData->getTaxvat())->toHtml() ?>
            <?php endif ?>

            <?php $_gender = $block->getLayout()->createBlock(\Magento\Customer\Block\Widget\Gender::class) ?>
            <?php if ($_gender->isEnabled()): ?>
                <?= $_gender->setGender($formData->getGender())->toHtml() ?>
            <?php endif ?>
            <?= $block->getChildHtml('fieldset_create_info_additional') ?>
        </div>
    </fieldset>
    <?php if ($block->getShowAddressFields()): ?>
        <?php $cityValidationClass = $addressHelper->getAttributeValidationClass('city'); ?>
        <?php $postcodeValidationClass = $addressHelper->getAttributeValidationClass('postcode'); ?>
        <?php $regionValidationClass = $addressHelper->getAttributeValidationClass('region'); ?>
        <fieldset class="fieldset address">
            <legend class="legend"><span><?= $escaper->escapeHtml(__('Address Information')) ?></span></legend><br>
            <input type="hidden" name="create_address" value="1" />

            <?php $_company = $block->getLayout()->createBlock(\Magento\Customer\Block\Widget\Company::class) ?>
            <?php if ($_company->isEnabled()): ?>
                <?= $_company->setCompany($formData->getCompany())->toHtml() ?>
            <?php endif ?>

            <?php $_telephone = $block->getLayout()->createBlock(\Magento\Customer\Block\Widget\Telephone::class) ?>
            <?php if ($_telephone->isEnabled()): ?>
                <?= $_telephone->setTelephone($formData->getTelephone())->toHtml() ?>
            <?php endif ?>

            <?php $_fax = $block->getLayout()->createBlock(\Magento\Customer\Block\Widget\Fax::class) ?>
            <?php if ($_fax->isEnabled()): ?>
                <?= $_fax->setFax($formData->getFax())->toHtml() ?>
            <?php endif ?>

            <?php
                $_streetValidationClass = $addressHelper->getAttributeValidationClass('street');
            ?>

            <div class="field street required">
                <label for="street_1" class="label">
                    <span><?= /* @noEscape */ $block->getAttributeData()->getFrontendLabel('street') ?></span>
                </label>
                <div class="control">
                    <input type="text"
                           name="street[]"
                           value="<?= $escaper->escapeHtmlAttr($formData->getStreet(0)) ?>"
                           title="<?= /* @noEscape */ $block->getAttributeData()->getFrontendLabel('street') ?>"
                           id="street_1"
                           class="input-text <?= $escaper->escapeHtmlAttr($_streetValidationClass) ?>" maxlength="90">
                    <div class="nested">
                        <?php
                            $_streetValidationClass = trim(str_replace('required-entry', '', $_streetValidationClass));
                            $streetLines = $addressHelper->getStreetLines();
                        ?>
                        <?php for ($_i = 2, $_n = $streetLines; $_i <= $_n; $_i++): ?>
                            <div class="field additional">
                                <label class="label" for="street_<?= /* @noEscape */ $_i ?>">
                                    <span><?= $escaper->escapeHtml(__('Address')) ?></span>
                                </label>
                                <div class="control">
                                    <input type="text"
                                           name="street[]"
                                           value="<?= $escaper->escapeHtml($formData->getStreetLine($_i - 1)) ?>"
                                           title="<?= $escaper->escapeHtmlAttr(__('Street Address %1', $_i)) ?>"
                                           id="street_<?= /* @noEscape */ $_i ?>"
                                           class="input-text <?= $escaper->escapeHtmlAttr($_streetValidationClass) ?>" maxlength="70">
                                </div>
                            </div>
                        <?php endfor; ?>
                    </div>
                </div>
            </div>

            <?php if ($addressHelper->isVatAttributeVisible()): ?>
                <?php $_vatidValidationClass = $addressHelper->getAttributeValidationClass('vat_id'); ?>
                <div class="field taxvat">
                    <label class="label" for="vat_id">
                        <span><?= /* @noEscape */ $block->getAttributeData()->getFrontendLabel('vat_id') ?></span>
                    </label>
                    <div class="control">
                        <input type="text"
                               name="vat_id"
                               value="<?= $escaper->escapeHtmlAttr($formData->getVatId()) ?>"
                               title="<?= /* @noEscape */ $block->getAttributeData()->getFrontendLabel('vat_id') ?>"
                               class="input-text <?= $escaper->escapeHtmlAttr($_vatidValidationClass) ?>"
                               id="vat_id" maxlength="50">
                    </div>
                </div>
            <?php endif; ?>

            <div class="field country required">
                <label for="country" class="label">
                    <span><?= /* @noEscape */ $block->getAttributeData()->getFrontendLabel('country_id') ?></span>
                </label>
                <div class="control">
                    <?= $block->getCountryHtmlSelect() ?>
                </div>
            </div>

            <div class="field region required">
                <label for="region_id" class="label">
                    <span><?= /* @noEscape */ $block->getAttributeData()->getFrontendLabel('region') ?></span>
                </label>
                <div class="control">
                    <select id="region_id"
                            name="region_id"
                            title="<?= /* @noEscape */ $block->getAttributeData()->getFrontendLabel('region') ?>"
                            class="validate-select region_id">
                        <option value="">
                            <?= $escaper->escapeHtml(__('Please select a region, state or province.')) ?>
                        </option>
                    </select>
                    <?= /* @noEscape */ $secureRenderer->renderStyleAsTag("display: none;", 'select#region_id') ?>
                    <input type="text"
                           id="region"
                           name="region"
                           value="<?= $escaper->escapeHtml($block->getRegion()) ?>"
                           title="<?= /* @noEscape */ $block->getAttributeData()->getFrontendLabel('region') ?>"
                           class="input-text <?= $escaper->escapeHtmlAttr($regionValidationClass) ?>" >
                    <?= /* @noEscape */ $secureRenderer->renderStyleAsTag("display: none;", 'input#region') ?>
                </div>
            </div>

            <div class="field required">
                <label for="city" class="label">
                    <span><?= /* @noEscape */ $block->getAttributeData()->getFrontendLabel('city') ?></span>
                </label>
                <div class="control">
                    <input type="text"
                           name="city"
                           value="<?= $escaper->escapeHtmlAttr($formData->getCity()) ?>"
                           title="<?= /* @noEscape */ $block->getAttributeData()->getFrontendLabel('city') ?>"
                           class="input-text <?= $escaper->escapeHtmlAttr($cityValidationClass) ?>"
                           id="city" maxlength="60">
                </div>
            </div>

            <div class="field zip required">
                <label for="zip" class="label">
                    <span><?= /* @noEscape */ $block->getAttributeData()->getFrontendLabel('postcode') ?></span>
                </label>
                <div class="control">
                    <input type="text"
                           name="postcode"
                           value="<?= $escaper->escapeHtmlAttr($formData->getPostcode()) ?>"
                           title="<?= /* @noEscape */ $block->getAttributeData()->getFrontendLabel('postcode') ?>"
                           id="zip"
                           class="input-text validate-zip-international
                            <?= $escaper->escapeHtmlAttr($postcodeValidationClass) ?>" maxlength="10">
                </div>
            </div>

            <?php $addressAttributes = $block->getChildBlock('customer_form_address_user_attributes');?>
            <?php if ($addressAttributes): ?>
                <?php $addressAttributes->setEntityType('customer_address'); ?>
                <?php $addressAttributes->setFieldIdFormat('address:%1$s')->setFieldNameFormat('address[%1$s]');?>
                <?php $block->restoreSessionData($addressAttributes->getMetadataForm(), 'address');?>
                <?= $addressAttributes->setShowContainer(false)->toHtml() ?>
            <?php endif;?>
            <input type="hidden" name="default_billing" value="1">
            <input type="hidden" name="default_shipping" value="1">
        </fieldset>

    <?php endif; ?>
    <div class="regi-tab">
        <h1>Secure your account</h1>
        <h2>Create a new account</h2>
        <fieldset class="fieldset create account"
                data-hasrequired="<?= $escaper->escapeHtmlAttr(__('* Required Fields')) ?>">
            <legend class="legend"><span><?= $escaper->escapeHtml(__('Sign-in Information')) ?></span></legend><br>
            <div class="field required">
                <label for="email_address" class="label"><span><?= $escaper->escapeHtml(__('Email')) ?></span></label>
                <div class="control">
                    <input type="email"
                        name="email"
                        autocomplete="email"
                        id="email_address"
                        value="<?= $escaper->escapeHtmlAttr($formData->getEmail()) ?>"
                        title="<?= $escaper->escapeHtmlAttr(__('Email')) ?>"
                        class="input-text"
                        data-mage-init='{"mage/trim-input":{}}'
                        data-validate="{required:true, 'validate-email':true}"
                        placeholder="Email" maxlength="70">
                    <i class="icon-line-mail"></i>
                </div>
            </div>
            <div class="field password required">
                <label for="password" class="label"><span><?= $escaper->escapeHtml(__('Password')) ?></span></label>
                <div class="control">
                    <input type="password" name="password" id="password"
                        title="<?= $escaper->escapeHtmlAttr(__('Password')) ?>"
                        class="input-text"
                        data-password-min-length="<?=
                            $escaper->escapeHtmlAttr($block->getMinimumPasswordLength()) ?>"
                        data-password-min-character-sets="<?=
                            $escaper->escapeHtmlAttr($block->getRequiredCharacterClassesNumber()) ?>"
                        data-validate="{required:true, 'validate-customer-password':true}"
                        autocomplete="off"
                        placeholder="Password" maxlength="35">
                        <i class="icon-line-lock"></i>
                    <div id="password-strength-meter-container" data-role="password-strength-meter" aria-live="polite">
                        <div id="password-strength-meter" class="password-strength-meter">
                            <?= $escaper->escapeHtml(__('Password Strength')) ?>:
                            <span id="password-strength-meter-label" data-role="password-strength-meter-label">
                                <?= $escaper->escapeHtml(__('No Password')) ?>
                            </span>
                        </div>
                    </div>
                </div>

            </div>

            <div class="field confirmation required">
                <label for="password-confirmation" class="label">
                    <span><?= $escaper->escapeHtml(__('Confirm Password')) ?></span>
                </label>
                <div class="control">
                    <input type="password"
                        name="password_confirmation"
                        title="<?= $escaper->escapeHtmlAttr(__('Confirm Password')) ?>"
                        id="password-confirmation"
                        class="input-text"
                        data-validate="{required:true, equalTo:'#password'}"
                        autocomplete="off"
                        placeholder="Confirm password"
                        maxlength="35" >
                    <i class="icon-line-lock"></i>
                </div>
            </div>
            <div class="field choice" data-bind="scope: 'showPassword'">
                <!-- ko template: getTemplate() --><!-- /ko -->
            </div>

            <div class="field required" id="wk-termsconditions-box">
                <div class="input-box">
                    <input type="checkbox" name="wk_termsconditions"
                    class="checkbox required-entry validation-failed" id="wk-termsconditions" data-validate='{"required":true}'>
                    <label for="wk-termsconditions"><?php echo __("By clicking, you confirm that you have read, understood, and agreed to ComAve's"); ?>

                    <a href="<?php echo $values."terms-condition-comave"; ?>" title="Click here"
                    target="_blank"><?php echo __('Terms & Condition,'); ?></a>

                    <a href="<?php echo $values."privacy-policy-comave"; ?>" title="Click here"
                    target="_blank"><?php echo __('Privacy Policy'); ?></a><?php echo __(' and '); ?>

                    <a href="<?php echo $values."data-protection-policy"; ?>" title="Click here"
                    target="_blank"><?php echo __('Data Protection Policy.'); ?></a>

                    </label>
                </div>
            </div>
            <div class="actions-toolbar">
                <div class="primary">
                    <button type="submit"
                            class="action submit primary"
                            title="<?= $escaper->escapeHtmlAttr(__('Create Account')) ?>" onclick="onkeep()">
                        <span><?= $escaper->escapeHtml(__('Create Account')) ?></span>
                    </button>
                </div>
            </div>

        </fieldset>
    </div>

    <fieldset class="fieldset additional_info">
        <?= $block->getChildHtml('form_additional_info') ?>
    </fieldset>

    <div style="overflow:auto;">
        <div style="float:right;">
            <button type="button" id="nextBtn">Next</button>
            <button type="button" id="prevBtn">Previous</button>
        </div>
        <div class="secondary">
            <div class="sign-in">Already have an account?
                <a class="action login"
                    href="<?= $block->getUrl('customer/account/login'); ?>">
                    <span><?= $escaper->escapeHtml(__('Sign in')) ?></span>
                </a>
            </div>
            <a class="action back"
                href="<?= $escaper->escapeUrl($block->getBackUrl()) ?>">
                <span><?= $escaper->escapeHtml(__('Back')) ?></span>
            </a>
        </div>
    </div>
</form>
<div class="register-section">
    <div class="regi-img"><img src="<?php echo $blockClub->getMediaUrl() . 'wysiwyg/pearl_theme/comave-register.png'; ?>"></div>
    <div class="regi-img"><img src="<?php echo $blockClub->getMediaUrl() . 'wysiwyg/pearl_theme/comave-register2.png'; ?>"></div>
    <div class="regi-img"><img src="<?php echo $blockClub->getMediaUrl() . 'wysiwyg/pearl_theme/comave-register3.png'; ?>"></div>
    <div class="regi-img"><img src="<?php echo $blockClub->getMediaUrl() . 'wysiwyg/pearl_theme/comave-register.png'; ?>"></div>
</div>
</div>
<?php $ignore = /* @noEscape */ $_dob->isEnabled() ? '\'input[id$="full"]\'' : 'null';
$scriptString = <<<script
require([
    'jquery',
    'mage/mage'
], function($){

    var dataForm = $('#form-validate');
    var ignore = {$ignore};

    dataForm.mage('validation', {
script;
if ($_dob->isEnabled()):
    $scriptString .= <<<script
        errorPlacement: function(error, element) {
            if (element.prop('id').search('full') !== -1) {
                var dobElement = $(element).parents('.customer-dob'),
                    errorClass = error.prop('class');
                error.insertAfter(element.parent());
                dobElement.find('.validate-custom').addClass(errorClass)
                    .after('<div class="' + errorClass + '"></div>');
            }
            else {
                error.insertAfter(element);
            }
        },
        ignore: ':hidden:not(' + ignore + ')'
script;
else:
    $scriptString .= <<<script
        ignore: ignore ? ':hidden:not(' + ignore + ')' : ':hidden'
script;
endif;
$scriptString .= <<<script
    }).find('input:text').attr('autocomplete', 'off');
});
script;
?>
<?= /* @noEscape */ $secureRenderer->renderTag('script', [], $scriptString, false) ?>
<?php if ($block->getShowAddressFields()): ?>
    <?php
    $regionJson = /* @noEscape */ $regionProvider->getRegionJson();
    $regionId = (int) $formData->getRegionId();
    $countriesWithOptionalZip = /* @noEscape */ $directoryHelper->getCountriesWithOptionalZip(true);
    ?>
<script type="text/x-magento-init">
    {
        "#country": {
            "directoryRegionUpdater": {
                "optionalRegionAllowed": <?= /* @noEscape */ $displayAll ? 'true' : 'false' ?>,
                "regionListId": "#region_id",
                "regionInputId": "#region",
                "postcodeId": "#zip",
                "form": "#form-validate",
                "regionJson": <?= /* @noEscape */ $regionJson ?>,
                "defaultRegion": <?= /* @noEscape */ $regionId ?>,
                "countriesWithOptionalZip": <?=  /* @noEscape */ $countriesWithOptionalZip ?>
            }
        }
    }
</script>
<?php endif; ?>

<script type="text/x-magento-init">
    {
        ".field.password": {
            "passwordStrengthIndicator": {
                "formSelector": "form.form-create-account"
            }
        },
        "*": {
            "Magento_Customer/js/block-submit-on-send": {
                "formId": "form-validate"
            },
            "Magento_Ui/js/core/app": {
                "components": {
                    "showPassword": {
                        "component": "Magento_Customer/js/show-password",
                        "passwordSelector": "#password,#password-confirmation"
                    }
                }
            }
        }
    }
</script>
<script type="text/javascript">
    function onkeep() {
        var dialCodeDiv = document.querySelector('.iti__selected-dial-code');
        var dialCode = dialCodeDiv.textContent.trim();
        var countryCode = dialCodeDiv.textContent.trim();
        document.getElementById('dial_code').value = dialCode;
        document.getElementById('country_codes').value = countryCode;
        // var phoneNumberInput = document.getElementById('phone_no');
        // var phoneNumber = phoneNumberInput.value.trim();
        // if (!phoneNumber.startsWith(dialCode)) {
        //     var combinedNumber = dialCode + phoneNumber;
        //     document.getElementById('phone_no').value = combinedNumber;
        // }
        var selectedFlagElement = document.querySelector('.iti__selected-flag');
        var titleAttribute = selectedFlagElement.getAttribute('title');
        var countryName = titleAttribute;

        if (titleAttribute.includes('(')) {
            countryName = titleAttribute.split('(')[0].trim();
        }
        countryName = countryName.split(':')[0].trim();
        document.getElementById('customer_country').value = countryName;

        var phoneNo = document.getElementById('phone_no').value;
        //var dialCode = document.getElementById('country_codes').value;
        document.getElementById('mobile').value = phoneNo;
        //document.getElementById('country_codes').value = dialCode;
        //document.getElementById('region').value = dialCode;
    }
</script>

<script>
require([
    'jquery',
    'mage/mage'
], function($){
    $(document).ready(function() {

    var currentTab = 0; // Current tab is set to be the first tab (0)

    showTab(currentTab); // Display the current tab

    $('.field.club-drop input[type="radio"]').on('change', function() {
        $('.field.club-drop label').removeClass('checked');
        $(this).parent().addClass('checked');
    });
    $('.field.copa-country input[type="radio"]').on('change', function() {
        $('.field.copa-country label').removeClass('checked');
        $(this).parent().addClass('checked');
    });

    function showTab(n) {
        var x = $(".regi-tab");
        x.eq(n).show();

        var img = $(".regi-img");
        img.eq(n).show();

        if (n == 0) {
        $("#prevBtn").hide();
        } else {
        $("#prevBtn").show();
        }
        if (n == (x.length - 1)) {
        $("#nextBtn").hide();
        } else {
        $("#nextBtn").show();
        $("#nextBtn").text("Next");
        }
        fixStepIndicator(n);
    }

    function nextPrev(n) {
        var x = $(".regi-tab");
        var img = $(".regi-img");


        if (n == 1 && !validateForm()) return false;
        x.eq(currentTab).hide();
        img.eq(currentTab).hide();
        currentTab = currentTab + n;
        if (currentTab >= x.length) {
        $("#form-validate").submit();
        return false;
        }
        showTab(currentTab);
    }

    function validateForm() {
        var x, y, i, valid = true;
        x = $(".regi-tab");


        y = x.eq(currentTab).find("input[type=text]");

        for (i = 0; i < y.length; i++) {
        if (y[i].value == "") {
            $(y[i]).addClass("invalid");
            valid = false;
        } else {
            $(y[i]).removeClass("invalid");
        }
        }
        if (valid) {
        $(".step").eq(currentTab).addClass("finish");
        }
        return valid;
    }

    function fixStepIndicator(n) {
        var i, x = $(".step");
        x.removeClass("active");
        x.eq(n).addClass("active");
    }

    $("#prevBtn").click(function() {

        nextPrev(-1);
    });

    $("#nextBtn").click(function() {

        nextPrev(1);
    });
    });

});
</script>
